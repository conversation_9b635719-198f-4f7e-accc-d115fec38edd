patch:
  speller/algebra:
    __patch:
      - wanxiang_pro.schema:/小鹤双拼            # 可选输入方案名称：自然码, 自然龙, 小鹤双拼, 搜狗双拼, 微软双拼, 智能ABC, 紫光双拼, 国标双拼
      - wanxiang_pro.schema:/直接辅助            #辅助码升级为：直接辅助和间接辅助两种类型，都是句中任意，不同点在于直接辅助是nire=你  而间接则需要/引导  ni/re=你 ，在这个基础上直接辅助支持拼音后任意位置数字声调参与，间接辅助声调在/引导前参与
  # 中英混合词汇，要与你的双拼类型一样
  cn_en/user_dict: en_dicts/flypy           # 可选的值有：en_dicts/pinyin， en_dicts/zrm， en_dicts/flypy ，en_dicts/mspy， en_dicts/sogou
  #通过下面的设置可以让你自己的文件引入而与仓库custom_phrase.txt不同，以后可以大胆覆盖更新
  custom_phrase/user_dict: custom_phrasexx    # 改成什么就需要手动创建 xxxxxx.txt 文件在用户目录，这个文件主要用于置顶，编码为自定义编码的词汇
  translator/packs/+:
    - userxx                                  #导入根目录下名称为userxx.dict.yaml的自定义固定词典，编码要与固定词库一致（或者不写编码），形如姓名、专有名词公司名称等等
    #下面是候选数量，未来7890分别代表1234声，请候选长度不要大于6避免冲突
  # 候选数量
  menu/page_size: 5
  #生日信息：/sr或者osr，在这里定义全局替换构建你的生日查询数据库
  birthday_reminder:  #日期格式：必须是4位数字，格式为MMDD（月份和日期），例如：1月27日 → 0127 ，#备注格式：在日期后添加逗号，然后添加任意文本作为备注，例如："0501,我的好朋友"，也可以无备注
    solar_birthdays:  # 公历生日, 姓名: "日期,备注" or 姓名: "日期"
      小明: "0501,准备礼物"
      大明: "0405"
    lunar_birthdays:  # 农历生日, 姓名: "日期,备注" or 姓名: "日期"
      小明: "0114"
      小红: "0815,农历中秋"
  #下面用来改变你的windows小狼毫右下角软件图标
  #schema/+:
  #  icon: "icons/zhong.ico"
  #  ascii_icon: "icons/ying.ico"
  #下面这个可以改变tips上屏的按键
  key_binder/tips_key: "period"   #修改时候去default找，默认是句号
  #下面这个是修改快符的映射，按自己需求来
  quick_symbol_text:
    q: "‰"
    w: "？"
    e: "（"
    r: "）"
    t: "~"
    y: "·"
    u: "『"
    i: "』"
    o: "〖"
    p: "〗"
    a: "！"
    s: "……"
    d: "、"
    f: "“"
    g: "”"
    h: "‘"
    j: "’"
    k: "【"
    l: "】"
    z: "。”"
    x: "？”"
    c: "！”"
    v: "——"
    b: "%"
    n: "《"
    m: "》"
    "1": "①"
    "2": "②"
    "3": "③"
    "4": "④"
    "5": "⑤"
    "6": "⑥"
    "7": "⑦"
    "8": "⑧"
    "9": "⑨"
    "0": "⓪"
  
  switches:
    - name: ascii_mode                   # 中英输入状态
      states: [ 中文, 英文 ]
    - name: ascii_punct                  # 中英标点
      states: [ 中标, 英标 ]
    - name: full_shape                   #全角、半角字符输出
      states: [ 半角, 全角 ]
    - name: emoji                        #候选出现emoji滤镜，会显示在相应的候选后面，万象侧重于tips提示，避免候选被占用，因此默认为reset: 0，归属opencc emoji滤镜
      states: [ 🚫, 😄 ]
    - name: chinese_english              #候选进入翻译模式滤镜，会显示在相应的候选后面，万象侧重于tips提示，避免候选被占用，快捷键配套ctrl+e,归属opencc 翻译滤镜
      states: [ 翻译关, 翻译开 ]
    - name: tone_display                 #开启后在输入编码的位置实时转换为带声调全拼，不开启则采用系统配置，快捷键配套ctrl+s,影响的是preedit_format,归属：super_preedit.lua
      states: [ 声调关, 声调开 ]
    - name: chaifen_switch               #开启后在候选的注释里面实时显示辅助码的拆分提醒优先级高于普通辅助提醒，不开启则采用系统配置，快捷键配套ctrl+c,影响的是comment_format，归属：super_comment.lua
      states: [ 拆分关, 拆分开 ]
    - name: charset_filter               #字符集过滤，默认开启8105通规显示，即小字集，可通过开关实时开启全字集，快捷键配套ctrl+g,归属：chars_filter.lua
      states: [ 小字集, 大字集 ]
    - name: super_tips                   #开启后在输入编码后面的提示区显示实时的提示数据，受tips数据库影响，表情、翻译、车牌、符号等对应关系数据，并可实现句号上屏，不开启则默认，影响的是segment.prompt参数，归属：super_tips.lua
      states: [ 提示关, 提示开 ]
      reset: 1
    - options: [ comment_off, fuzhu_hint, tone_hint ]  #开启后在候选的注释里面实时显示辅助码或者全拼声调，不开启则采用系统配置，影响的是comment_format，快捷键配套ctrl+a,归属：super_comment.lua
      states: [ 注释关, 辅助开, 读音开 ]
    - options: [ s2s, s2t, s2hk, s2tw ]  # 简繁转换开关组，可以在一个空选项和多个实际“- simplifier@s2hk”引入的项目之前切换，这是一个开关组，你可以将其中任意一个s2s等设置为toggle快捷键，多次按下将轮询
      states: [ 简体, 通繁, 港繁, 臺繁 ]
    - name: prediction                   #开启后输入上屏后继续弹出预测数据候选展示例如输入：你在 ，预测候选： 哪里 干嘛 吗 做等等，predictor配置区可设置详细参数，归属：predict内置插件
      states: [ 预测关, 预测开 ]
    - name: search_single_char          #多体现在编码重合但候选有单字或者多字的情况`引导的辅码查词时是否单字优先，全拼常见，类似于特定编码情况下、反查状态下的调序能力。归属：search.lua
      states: [正常, 单字]
  
  #下面这两个是快符的引导符号，前者用来引导符号、双击重复上屏符号，后者双击重复上屏汉字
  recognizer/patterns/quick_symbol: "^;.*$"
  #下面这个用来设置开启调频的时候哪些内容不调频
  translator/disable_user_dict_for_patterns: "^[a-z]{1,6}"

  # 以词定字,使用 @before.0 添加到processors列表开头
  engine/processors/@before.0: lua_processor@*select_character
  
  # 分号键用于次选,使用 @before.0 添加到bindings的开头
  key_binder/bindings/@before.0: { when: has_menu, accept: semicolon, send: 2 }

  # 大写辅助码加到整句倒数第二个音节,使用 @append 添加到bindings的末尾
  key_binder/bindings/@append:
    - { when: has_menu, accept: Shift+A, send_sequence: "{Shift+Left}a{Shift+Right}" }
    - { when: has_menu, accept: Shift+B, send_sequence: "{Shift+Left}b{Shift+Right}" }
    - { when: has_menu, accept: Shift+C, send_sequence: "{Shift+Left}c{Shift+Right}" }
    - { when: has_menu, accept: Shift+D, send_sequence: "{Shift+Left}d{Shift+Right}" }
    - { when: has_menu, accept: Shift+E, send_sequence: "{Shift+Left}e{Shift+Right}" }
    - { when: has_menu, accept: Shift+F, send_sequence: "{Shift+Left}f{Shift+Right}" }
    - { when: has_menu, accept: Shift+G, send_sequence: "{Shift+Left}g{Shift+Right}" }
    - { when: has_menu, accept: Shift+H, send_sequence: "{Shift+Left}h{Shift+Right}" }
    - { when: has_menu, accept: Shift+I, send_sequence: "{Shift+Left}i{Shift+Right}" }
    - { when: has_menu, accept: Shift+J, send_sequence: "{Shift+Left}j{Shift+Right}" }
    - { when: has_menu, accept: Shift+K, send_sequence: "{Shift+Left}k{Shift+Right}" }
    - { when: has_menu, accept: Shift+L, send_sequence: "{Shift+Left}l{Shift+Right}" }
    - { when: has_menu, accept: Shift+M, send_sequence: "{Shift+Left}m{Shift+Right}" }
    - { when: has_menu, accept: Shift+N, send_sequence: "{Shift+Left}n{Shift+Right}" }
    - { when: has_menu, accept: Shift+O, send_sequence: "{Shift+Left}o{Shift+Right}" }
    - { when: has_menu, accept: Shift+P, send_sequence: "{Shift+Left}p{Shift+Right}" }
    - { when: has_menu, accept: Shift+Q, send_sequence: "{Shift+Left}q{Shift+Right}" }
    - { when: has_menu, accept: Shift+R, send_sequence: "{Shift+Left}r{Shift+Right}" }
    - { when: has_menu, accept: Shift+S, send_sequence: "{Shift+Left}s{Shift+Right}" }
    - { when: has_menu, accept: Shift+T, send_sequence: "{Shift+Left}t{Shift+Right}" }
    - { when: has_menu, accept: Shift+U, send_sequence: "{Shift+Left}u{Shift+Right}" }
    - { when: has_menu, accept: Shift+V, send_sequence: "{Shift+Left}v{Shift+Right}" }
    - { when: has_menu, accept: Shift+W, send_sequence: "{Shift+Left}w{Shift+Right}" }
    - { when: has_menu, accept: Shift+X, send_sequence: "{Shift+Left}x{Shift+Right}" }
    - { when: has_menu, accept: Shift+Y, send_sequence: "{Shift+Left}y{Shift+Right}" }
    - { when: has_menu, accept: Shift+Z, send_sequence: "{Shift+Left}z{Shift+Right}" }
