---

# 万象系列方案 [![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/amzxyz/rime_wanxiang)

---------------------

## 万象拼音——基于深度优化的词库和语言模型

[万象词库与万象语言模型](https://github.com/amzxyz/RIME-LMDG) 是一种带声调的词库，经过AI和大基数语料筛选、加频，结合语言模型获得更准确的整句输出。还结合了中英文混输，一套词库，多种用法，具体可以点击链接了解优势

### 优势

1. 词库词语全部加音调
2. 设计8种辅助码，头部使用全拼编码，可以转化为任何双拼编码
    - 词库解码顺序为：全拼拼音；墨奇码；鹤形；自然码；简单鹤；虎码首末；五笔前2；汉心码
    - 因此，万象拼音支持拼音和辅助码任意两两组合

**万象词库中的带声调拼音标注+词组构成+词频是整个万象项目的核心，是使用体验的基石，方案的其它功能皆可自定义，我希望使用者可以基于词库+转写的方式获得输入体验** [万象词库问题收集反馈表](https://docs.qq.com/smartsheet/DWHZsdnZZaGh5bWJI?viewId=vUQPXH&tab=BB08J2)

---------------------

**效果大赏**

![效果.png](https://storage.deepin.org/thread/202502200358104987_效果.png)

---------------------

### 新手快速入门

不了解rime基础的可以参考友情链接，初步了解rime运行的些许特性：

[oh my rime](https://www.mintimate.cc/zh/guide/installRime.html) 

[rime参数配置](https://xishansnow.github.io/posts/41ac964d.html)

整个rime配置生态都是通的，里面有非常完整的使用方法，(诸如放到哪里、换个皮肤、什么是用户目录。。。)

**友情提示：** *如果你是第一次使用万象，可以不要用你过往经验来定义万象，按如下的步骤将万象跑起来后,，体验一下万象的功能，然后学习一下万象的各项内容之后再考虑。*

对于新手，使用更新脚本进行安装万象方案，你无需关心路径问题、版本问题，只需要按照脚本提示作选择即可。脚本的下载地址 [万象方案更新脚本](https://github.com/expoli/rime-wanxiang-update-tools)。这里以win版本的小狼毫为例，更新脚本为 powershell 的utf-8版本。在使用万象之前，请安装小狼毫，安装小狼毫的过程中，请一切保持默认即可，等你熟悉之后可以自定义。本入门最终会带你一步步设置使用**小鹤双拼+墨奇辅助码**的方案。

1. 下载更新脚本，直接点击上面地址首页的下载链接即可。下载完成后，直接双击运行刚刚下载的ps1脚本。如果杀毒报错，请将你下载的文件恢复后，添加到信任文件。
2. 脚本执行后，需要你确认你使用的是全拼还是双拼用户，如果你是全拼用户，请根据提示输入 0 后回车。如果你双拼用户，需要你确认你使用的辅助码类型，默认的万象方案支持汉心、简单鹤、墨奇、虎码、五笔、自然码共计6种辅助码类型。如果没有你想要的辅助码类型，也不要着急，可在熟悉万象之后自定义。根据提示输入你使用的辅助码类型的数字后回车即可。这里我按墨奇辅助码输入。更不要着急怎么选择双拼方案，后面会告诉你的。
3. 脚本继续执行，提示选择是否全部更新，不要管，你是新手，直接选全部更新，输入 0 回车。然后就默默的等待脚本执行完后，按任意键退出即可。默认情况下，万象的双拼方案为自然码。
4. 现在开始新人激动时刻，**设置双拼方案**。为了你少走弯路，具体如下：

#### ①第一种方法：快速开始

由于万象**支持各种演变双拼编码方式**，所以不能一一列举，且方案文件多了容易混乱。然而，使用rime不可避免的要自定义，故而干脆将选项前置，初始化选择一番就是自己想要的方案，更深的定制再去修改其它地方。对于万象拼音方案文件内部有着详细的说明注释，我认为外部文档不能替代注释，如果你愿意花时间阅读方案文件、并结合功能名介绍和思维导图将能更加深入的了解

1. 打开`wanxiang.schema.yaml`文件，或者(wanxiang_pro.schema.yaml)表头按照备注提示填写即可，定义属于自己的输入法方案，这里需要注意yaml语法冒号后面的值与冒号之间有个空格，值后面的备注#与值也不能紧挨着

```yaml
    #本方案匹配词库解码顺序为：全拼拼音；墨奇；鹤形；自然码；简单鹤；虎码首末；五笔前2；汉心码
    #############DIY你想要的方案组合,试试搭配一个自然码+墨奇辅助的方案吧！###########################
set_shuru_schema:              #配置此项就是选择什么输入法,同时拆分反查和中英文混输也将匹配该输入方案
  __include: 自然码             #可选解码规则有   自然码, 自然龙, 汉心龙, 小鹤双拼, 搜狗双拼, 微软双拼, 智能ABC, 紫光双拼, 国标双拼， 龙三    选择一个填入
set_algebra_fuzhu:             #配置此项就是选择什么辅助码
  __include: fuzhu_zrm         #可选辅助码有：fuzhu_kong，fuzhu_hanxin, fuzhu_moqi, fuzhu_flypy, fuzhu_zrm, fuzhu_tiger, fuzhu_wubi    选择一个填入
set_fuzhu_type:                #直接辅助就是nire/=你，间接辅助就是ni/re=你，区别在于间接辅助不使用/引导的时候就和普通双拼没区别
  __include: 直接辅助           #可选的有：直接辅助、间接辅助
set_cn_en:                     #中英混输
  user_dict: en_dicts/pinyin   #可选的值有：en_dicts/pinyin， en_dicts/zrm， en_dicts/flypy ，en_dicts/mspy， en_dicts/sogou， en_dicts/pinyin
super_comment:                 # 超级注释模块，子项配置 true 开启，false 关闭
  candidate_length: 1          # 候选词辅助码提醒的生效长度，0为关闭  但同时清空其它，应当使用上面开关来处理    
  fuzhu_type: zrm              # 用于匹配对应的辅助码注释显示，基于默认词典的可选注释类型有：moqi, flypy, zrm, jdh, cj, tiger, wubi, hanxin 声调用开关切换
  corrector_type: "{comment}"  # 换一种显示类型，比如"({comment})" 
__include: octagram            #启用语言模型
#__include: set_chord_composer  #启用并击处理，不懂得不要开启就是了
    ########################以下是方案配置######################################################
```

2. 打开 `wanxiang_radical.schema.yaml` 和 `wanxiang_en.schema.yaml` 表头进行选择，二者情况一致：

```yaml
    ###############选择与之匹配的拼音方案#####################
set_shuru_schema:
  __include: 全拼    #可选的选项有（全拼, 自然码, 小鹤双拼, 微软双拼, 搜狗双拼, 智能ABC, 紫光双拼, 拼音加加）
    ######################################################
```

3. 保存后部署方案即可！

#### ②进阶custom patch法（已经尽量为你简化）强烈推荐

1. 如果方法①使用的打字效果你还满意计划长期使用，那么你可以进入这个模式，通过用户的补丁文件对主文件进行更改配置，从而实现在原有的基础上自定义的过程，这样能保证原始包可以直接覆盖升级，不影响你的数据和你的变更；
2. 打开用户文件夹，然后打开**custom**文件夹。
3. 打开wanxiang_pro.custom.yaml，找到 **- wanxiang_pro.schema:/小鹤双拼**，看后面的提示，你可以直接设置你要使用的双拼方案，比如你可以直接改为**自然码**；
4. 打开wanxiang_en.custom.yaml，直接找到 **__include: wanxiang_en.schema:/自然码** ，然后修改自然码为小鹤双拼。
5. 打开wanxiang_radical.custom.yaml，直接找 **__include: wanxiang_radical.schema:/全拼** ，然后修改全拼为小鹤双拼。
6. 上面三个文件设置完成后，将其**复制到用户目录**里，然后重新部署，完成后就可以使用小鹤双拼+墨奇辅助码了。
7. 注意：使用了此方法，方法①就失效了，填写的数据会被这个方法覆盖。其次这个方法给出的示例文件有诸多自定义的地方，每一行详细查阅、理解、修改。
8. 更详细参照：[🚀 Rime 万象拼音输入方案 新手安装配置指南](https://docs.qq.com/doc/DQ0FqSXBmYVpWVFpy?rtkey=)

### 答疑

#### 为什么词库这么大，我见过只有单字携带辅助码的方案，词库可以缩小吗？

在这里我借助wiki深入阐述一下这个问题并解答这些问题：[万象词库PRO的设计理念](https://github.com/amzxyz/RIME-LMDG/wiki/%E4%B8%87%E8%B1%A1%E8%AF%8D%E5%BA%93PRO%E7%9A%84%E8%AE%BE%E8%AE%A1%E7%90%86%E5%BF%B5)

[为什么PRO版本默认关闭调频的说明](https://github.com/amzxyz/RIME-LMDG/wiki/%E4%B8%BA%E4%BB%80%E4%B9%88%E8%A6%81%E5%85%B3%E9%97%AD%E8%B0%83%E9%A2%91%E4%BB%A5%E5%8F%8A%E4%B8%8E%E4%B9%8B%E5%85%B3%E8%81%94%E7%9A%84%E6%8E%AA%E6%96%BD%E6%9C%89%E5%93%AA%E4%BA%9B)   ```enable_user_dict: false # 是否开启自动调频，true为开启```

### 功能一览

#### 辅助码

辅助码可以在输入一个确定得拼音后面继续输入一个部首的读音，使得这个字出现在靠前甚至第一位。这种方式易于理解，无须记忆字根，一切基于拼音得基础上。例如：

![截图_选择区域_20240704121653.png](https://storage.deepin.org/thread/202407041144502563_截图_选择区域_20240704121653.png)

**功能1** **（仅PRO）** 如果想要 `镇` 字显示在前面  那么在本方案下提供两种方式，第一种就是辅助码声母，`vf`继续输入`j` 也就是金字旁得声母即可出现结果，如果还是出现不了你要的结果，可以输入另外主体字的声母来继续缩小范围。

![截图_选择区域_20240704121809.png](https://storage.deepin.org/thread/202407041147131421_截图_选择区域_20240704121809.png)

**功能2**  第二种方式是通过反查字库来定位，只是通过不同的方案实现，在输入主要拼音后，通过符号```  来引导进入反查状态，引导后继续输入`jn`金 则包含金的字就会被选出来；

![截图_选择区域_20240704121635.png](https://storage.deepin.org/thread/202407041149125588_截图_选择区域_20240704121635.png)

引导后继续输入`mu 木`则带`木`的字就会被选出来

![截图_选择区域_20240704121611.png](https://storage.deepin.org/thread/202407041149524870_截图_选择区域_20240704121611.png)

**功能3**  通过 拼音状态下``〔反查：部件|笔画〕`来引导拆字模式 举例 `震`  假设你不认识，你可以通过`雨和辰` 来合并输入，拼音状态输入后，继续输入其它字符字母az会消失如下图，输入 `yu if` 即雨 辰，结果出现了我们要的震字，且给出了辅助码 `y` 和  `i`  ，`y`是雨的声母`y`，`i`是辰的声母`ch`，同时通过hspnz分别代表横竖撇捺折，两种类型兼容。

 ![截图_选择区域_20240928112256.png](https://storage.deepin.org/thread/202409280324599355_截图_选择区域_20240928112256.png)

**功能4**  句子中间或者单字输入时需要输入全位辅助码时由于与双拼词语重码，因为我们设计的基本辅助码是2位，加上双拼共4位，由于在整句中我们为了整句输入的顺畅，不会将4码聚拢作为优先级较高的选择，这样会在很多时候造成你想打的句子缩成一团变成全辅助码的词汇。此时可以通过追加/的方式使其聚拢，这种方式是由于我们是直接辅助码导致的，如果我们通过一个符号引导辅助码，那么在输入时要每一个都用到符号，而采用这种方式我们只需要在必要的时候使用/节省了输入的按键开支，下面由两个图片说明问题：

![截图_选择区域_20240821093644.png](https://storage.deepin.org/thread/202408210142513354_截图_选择区域_20240821093644.png)

![截图_选择区域_20240821093701.png](https://storage.deepin.org/thread/202408210143144721_截图_选择区域_20240821093701.png)

**功能5**  句子中间或者单字输入时需要可以使用更精确的聚拢方式"声调辅助"，7890数字按键代表1234声，轻声归并到4声，在功能4中我们可以在双拼两码后面3个编码的位置任意插入声调与两位辅助码混合使用，就是除了不用斜杠了，我们还顺序自由了，下面由两个图片说明问题：

![截图_选择区域_20250512101814.png](https://storage.deepin.org/thread/202505120222182012_截图_选择区域_20250512101814.png)

![截图_选择区域_20250512101752.png](https://storage.deepin.org/thread/20250512022217432_截图_选择区域_20250512101752.png)

![截图_选择区域_20250512101713.png](https://storage.deepin.org/thread/202505120222163619_截图_选择区域_20250512101713.png)

#### 其他亮点功能

**日期、时间、节日、节气、问候模板：**
可以在按键配置的地方定制引导前缀

```key_binder/shijian_keys: ["/", "o"]``` 这样的配置以为你你可以/sj也可以osj，某些方案o有别的作用时候可以去掉o,灵活处理。

```yaml
#时间：osj 或者 /sj
#日期：orq 或者 /rq
#农历：onl 或者 /nl
#星期：oxq 或者 /xq
#今年第几周：oww 或者 /ww
#节气：ojq 或者 /jq
#日期+时间：ors 或者 /rs
#时间戳：ott 或者 /tt
#大写N日期：N20250315
#节日：ojr 或者 /jr
#问候模板：/day 或者 oday
```

**Unicode：** 大写 U 开头，如 U62fc 得到「拼」。

**数字、金额大写：**  大写 R 开头，如 R1234 得到「一千二百三十四、壹仟贰佰叁拾肆元整」。

 **/引导模式：**  通过输入 /sx 快捷输入关于“数学”的特殊符号，具体能输入什么可以打开 symbols.yaml学习。

**计算器：**  通过输入大写V引导继续输入如：V3+5  候选框就会有8和3+5=8，基础功能 `+ - * / % ^` 还支持 `sin(x) cos(x)` 等众多运算方式 [点击全面学习](https://github.com/gaboolic/rime-shuangpin-fuzhuma/blob/main/md/calc.md)

**自动上屏：**  例如：三位、四位简码唯一时，自动上屏如`jjkw岌岌可危` `zmhu怎么回事` 。默认未开启，方案文件中`speller:`字段下取消注释这两句开启 `#  auto_select: true  #  auto_select_pattern: ^[a-z]+/|^[a-df-zA-DF-Z]\w{3}|^e\w{4}`

**错音错字提示：**  例如：输入`gei yu给予`，获得`jǐ yǔ`提示，此功能与全拼、双拼类型无关全部支持；

**快符Lua：** 例如 ```;q``` 通过分号键引导的26字母快速符号自动上屏，双击;;重复上屏候选词，此功能会占用分号不能直接上屏，但带来的收益也是显而易见的；

**超级tips：** 支持将表情、化学式、翻译、简码 提示等等你能想到得数据获得提示显示并将通过一个自定义按键直接上屏，默认为“.” 避免了占用候选框，通过Control+t 进行开关；

**辅助码提示（仅PRO）：** 任意长度候选词的辅助码提示能力，默认开启1个字的辅助码，可以在方案文件中定义更长的长度。Ctrl+a可以实时在开启辅助码提示、开启声调全拼提示、关闭注释 三个状态循环，Ctrl+c开启拆分辅助提示，优先级高于普通辅助提示；

**输入码音调显示：** 通过Ctrl+s可以使得输入码实时动态显示全拼并加音调，这是万象特色功能；

**用户按需造词（仅PRO）：** 默认通过``` `` ```引导的方式进入用户词自造词模式，继续输入则``` `` ```前缀消失，后面打出来的字上屏后完成造词。 pro版本讲究自主可控，由于辅助码的使用在很多时候不熟悉的时候可能会上屏更加异常的词汇或者生僻字，有的用户还不会使用Esc退出输入，而是选择直接敲下空格。按需造词可以有效把控造出的词是有意义的，而且默认靠后，原因简单基本上有意义的高频词万象已经提供，你应该使用辅助码将其前置。**重点**：在此基础上我们还支持“后触发”当你输入编码后发现没有你要的行业词汇，此时在后面双击``` `` ``` 就可以在不删除编码的情况下完成造词。还有一个是次选造词，如果次选是你想要的，并且是词库组合成的，上屏就会记录下来。

总结一下，造词功能由：①``` `` ```起始的主动造词，②``` `` ```在编码后面的主动造词，③次选造词。三个特性构成

**用户词删除：** 不管什么删除都不能直接作用于固定词典，使用Ctrl+del是rime系统删除用户词,就可以将用户词标记为c<=0，这在rime系统中就表现为不使用，假性删除，如何能真的删除这些词汇，可以通过/del输入编码来触发删除，这是一个危险操作，操作之前需要点击同步触发导出用户词的txt文件，此后我们就能放心使用，整个步骤：①先同步，②输入/del触发清理③重新部署④同步，就可以将清理后的词库恢复到db数据库中。

**手动排序（Lua）：** ①词典候选类型：对选中的候选词操作，使用Ctrl+j向左一步，Ctrl+k向右一步，Ctrl+l(零)移除选中排序信息，Ctrl+p 置顶选中候选。其作用于当时编码与候选词；②动态生成的Lua候选，很多时候我们对日期、时间等输出格式首选有着自己的追求，复杂的配置又往往提升了使用难度，于是我们基于排序Lua实现了动态内容的按序号索引的排序，也就是说该序号下原本生成的格式整个发生了位置变化，使用方法一致。信息储存于Lua文件夹下排序数据库中sequence.userdb，支持导出导入数据便于多设备共用。

**声调辅助回退（Lua）：**万象是将7890用于代表1234声，轻声归并到了4，我们支持在例如输入ni9后发现我可能要4声，ni0，此时我们无需删除数字9而是直接输入对的0，类似手动在7890之间轮巡，能有效快速提升声调辅助的效率，减少使用负担，也是万象独创功能。

**删除键限制（Lua）：** 可以在输入中当持续按下删除编码为0时会卡住，抬起重新按下才能继续删除已经上屏内容，避免误删除上屏内容。目前仅PC可用，也是万象独创功能。

**输入长度限制（Lua）：** 对两类场景进行限制，避免数据并发卡顿：1、重复输入8个连续相同的字母，aaaaaaaa会提示:已超最大重复声母。因为连续多个的重复字母会造成严重的卡顿；2、分词片段限制在30个，也就是30个字，过长的语句价值不大还会造成卡顿。

**Tab循环切换音节：**  当输入多个字词时想要给前面补充辅助码，可以多次按下tab循环切换，这种可能比那些复杂的快捷键好用一些；

**翻译模式：**  输入状态按下Ctrl+E快捷键进入翻译模式，原理是opencc查表进行中英文互译，能否翻译取决于词表的丰富度；

 **反查：** 支持``` ` ```引导状态下的显示格式化，同时支持部件组字模式和笔画模式。反查模式不受字符集过滤影响，默认开放大字集,也不受辅助码开关影响，会显示注释，如：功能2相关展示；

**字符集过滤：** 默认开启过滤，写在charset.dict.yaml的就是可以通过的字表，默认为8105+𰻞𰻞，如果你想什么字在小字集模式可以通过可以写在这里，配套开关【小字集、大字集】，快捷键Ctrl+g 

**自定义词库：** 自定义词库首先要利用[LMDG](https://github.com/amzxyz/RIME-LMDG)中的脚本将你自己的词库刷成与万象同类型的声调、或者声调+辅助码的形态，因为主词库要参与转写。对于custom_phrase则需要手动编辑编码为实际输入的编码

![思维导图](https://github.com/amzxyz/rime_wanxiang_pro/blob/main/.github/%E4%B8%87%E8%B1%A1%E8%BE%93%E5%85%A5%E6%96%B9%E6%A1%88.png)

## 鸣谢

- 项目英文词库部分来自"[rime-ice](https://github.com/iDvel/rime-ice)"
- 拼音标注来自万象词库与语法模型项目，并在该项目下进行鸣谢！
- 感谢网友的热情提报问题，使得模型和词库体验进一步提升。

## 赞赏

如果觉得项目好用，可以请AMZ喝咖啡

<img alt="pay" src="./.github/赞赏.jpeg" height="312" width="446">
